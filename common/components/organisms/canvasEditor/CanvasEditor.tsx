'use client'

import React from 'react';
import {
  Stage, Layer,
} from 'react-konva';
import { motion } from 'framer-motion';
import { cn } from '@/common/utils/helpers';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { LayersPanel } from './LayersPanel';
import { LoadingOverlay } from './LoadingOverlay';
import {
  CanvasLoadingProvider,
} from './CanvasLoadingContext';
import { useCanvasEditor } from './useCanvasEditor';
import { PictureLandscapeIcon } from '../../icons';

export const addCursorHandlers = (node: any) => {
  node.on('mouseenter', () => {
    document.body.style.cursor = 'move';
  });
  node.on('mouseleave', () => {
    document.body.style.cursor = 'default';
  });
};

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

const CanvasEditorContent = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const {
    stageRef,
    canvasContainerRef,
    konvaStage,
    zoomLevel,
    isManualZoom,
    isInitialized,
    showLayers,
    setShowLayers,
    isDragOver,
    backgroundColor,
    menuPosition,
    showMenu,
    stageSize,
    handleContextMenu,
    handleDeleteNode,
    handleBringToFront,
    handleSendToBack,
    handleFitToCanvas,
    updateBackgroundColor,
    handleZoomChange,
    fitToView,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleSaveDesign,
    handleStageClick,
    loadingStates,
  } = useCanvasEditor({
    isOpen,
    platform,
    initialImage,
    agentId,
    planId,
    onSave,
  });

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
      />
      <motion.div
        className="flex flex-1 min-h-0 flex-col md:flex-row"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          duration: 0.6,
          delay: 1.2,
        }}
      >
        <motion.div
          className="block"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            duration: 0.5,
            delay: 1.4,
          }}
        >
          {konvaStage && isInitialized && (
            <CanvasSidebar
              canvas={konvaStage}
              agentId={agentId}
              planId={planId}
              containerRef={canvasContainerRef}
              zoomLevel={zoomLevel}
              onClose={onClose}
              loadingStates={loadingStates}
            />
          )}
        </motion.div>
        <motion.div
          className="flex-1 bg-neutral-800 flex flex-col"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            duration: 0.6,
            delay: 1.6,
          }}
        >
          <div
            ref={canvasContainerRef}
            className={cn(
              "flex-1 w-full h-full overflow-auto scroll-smooth relative",
              isDragOver && "bg-violets-are-blue/10",
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div
              className="flex items-center justify-center"
              style={{
                minHeight: `calc(100% + ${Math.max(200, stageSize.height * zoomLevel * 0.5)}px)`,
                minWidth: `calc(100% + ${Math.max(200, stageSize.width * zoomLevel * 0.5)}px)`,
                padding: '100px',
              }}
            >
              <div
                style={{
                  transform: `scale(${zoomLevel})`,
                  transformOrigin: 'center center',
                  transition: isManualZoom ? 'none' : 'transform 0.2s ease-out',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Stage
                  ref={stageRef}
                  onClick={handleStageClick}
                  onContextMenu={handleContextMenu}
                  width={stageSize.width}
                  height={stageSize.height}
                  className="block overflow-hidden bg-white "
                >
                  <Layer />
                </Stage>
                {isDragOver && (
                  <div className="absolute inset-0 bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center z-50">
                    <div className="bg-neutral-900/90 rounded-3xl p-6 text-center">
                      <div className="text-2xl mb-2 flex justify-center"><PictureLandscapeIcon /></div>
                      <p className="text-white font-medium">Drop image here</p>
                      <p className="text-gray-400 text-sm">PNG, JPG, SVG, GIF, WebP up to 10MB</p>
                    </div>
                  </div>
                )}
                <LoadingOverlay
                  isVisible={loadingStates.isSaving || loadingStates.isGenerating || loadingStates.isVectorizing || loadingStates.isImproving}
                  type={
                    loadingStates.isSaving ? "saving" :
                      loadingStates.isGenerating ? "generating" :
                        loadingStates.isVectorizing ? "vectorizing" : "improving"
                  }
                  message={
                    loadingStates.isSaving ? "Saving your design..." :
                      loadingStates.isGenerating ? "Creating your image with AI..." :
                        loadingStates.isVectorizing ? "Generating vector image..." :
                          loadingStates.isImproving ? "Enhancing your image with AI..." : "Processing..."
                  }
                />

                {showMenu && (
                  <div
                    style={{
                      position: 'absolute',
                      top: menuPosition.y,
                      left: menuPosition.x,
                      zIndex: 1000,
                    }}
                    onClick={(e) => e.stopPropagation()}
                    className="bg-neutral-900 rounded-2xl overflow-hidden shadow-xl border border-neutral-700 py-1 w-[240px]"
                  >
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                    >
                      Remove Background
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                    >
                      Enhance
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                    >
                      Vectorize
                    </button>
                    <div className="border-t border-neutral-700 my-1"></div>
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                      onClick={handleFitToCanvas}
                    >
                      Fit to Frame
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                      onClick={handleBringToFront}
                    >
                      Bring to Front
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-gray-200 hover:bg-neutral-800 transition-colors duration-150"
                      onClick={handleSendToBack}
                    >
                      Send to Back
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-red-400 hover:bg-neutral-800 transition-colors duration-150"
                      onClick={handleDeleteNode}
                    >
                      Delete
                    </button>
                  </div>
                )}
              </div>
            </div>
            {showLayers && (
              <LayersPanel
                canvas={konvaStage}
                onClose={() => setShowLayers(false)}
              />
            )}
          </div>
          <motion.div
            className="text-center text-gray-500 text-xs"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.4,
              delay: 2.0,
            }}
          >
            <div className="flex flex-col md:flex-row items-center justify-between gap-2 md:gap-4">
              {/* <p className="text-center md:text-left">Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p> */}
              <div></div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  duration: 0.5,
                  delay: 1.8,
                }}
              >
                <FloatingToolbar
                  canvas={konvaStage}
                  zoomLevel={zoomLevel}
                  onZoomChange={handleZoomChange}
                  onFitToView={fitToView}
                  showLayers={showLayers}
                  onToggleLayers={() => setShowLayers(!showLayers)}
                  backgroundColor={backgroundColor}
                  onBackgroundColorChange={updateBackgroundColor}
                />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export const CanvasEditor = (props: CanvasEditorProps) => {
  return (
    <CanvasLoadingProvider>
      <CanvasEditorContent {...props} />
    </CanvasLoadingProvider>
  );
};

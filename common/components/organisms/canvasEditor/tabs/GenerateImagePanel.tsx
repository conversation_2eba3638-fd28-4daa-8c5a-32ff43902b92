'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import Konva from 'konva';
import autosize from 'autosize';
import { ImageStyleGroups } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';
import { useCanvasLoading } from '../CanvasLoadingContext';

interface GenerateImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
}

const scaleImageToFitCanvas = (
  img: Konva.Image,
  canvas: Konva.Stage,
) => {
  if (!canvas || !canvas.children || canvas.children.length === 0) {
    console.error('Canvas not properly initialized');
    return;
  }

  let layer = canvas.findOne('Layer') as Konva.Layer;
  if (!layer) {
    layer = new Konva.Layer();
    canvas.add(layer);
  }

  const canvasWidth = canvas.width();
  const canvasHeight = canvas.height();
  const imageElement = img.image() as HTMLImageElement;
  const imgWidth = imageElement?.width || 1;
  const imgHeight = imageElement?.height || 1;

  const scaleX = canvasWidth / imgWidth;
  const scaleY = canvasHeight / imgHeight;
  const scale = Math.min(scaleX, scaleY);

  img.x((canvasWidth - imgWidth * scale) / 2);
  img.y((canvasHeight - imgHeight * scale) / 2);
  img.scaleX(scale);
  img.scaleY(scale);
  img.draggable(true);

  addCursorHandlers(img);

  layer.add(img);

  let transformer = layer.findOne('Transformer') as Konva.Transformer;
  if (!transformer) {
    transformer = new Konva.Transformer();
    layer.add(transformer);
  }

  transformer.nodes([img]);

  canvas.batchDraw();
};

const addWebPImageToCanvas = async (
  imageUrl: string,
  canvas: Konva.Stage,
  activeProject: { project_id: string } | null,
  agentId?: string,
  planId?: string,
  imagePrompt?: string,
) => {
  if (!canvas) {
    console.error('Canvas not available');
    throw new Error('Canvas not available');
  }

  if (!canvas.children || canvas.children.length === 0) {
    console.error('Canvas not ready for image addition');
    throw new Error('Canvas not ready. Please try again.');
  }

  return new Promise<void>((resolve, reject) => {
    const imageObj = new window.Image();
    imageObj.crossOrigin = 'anonymous';

    imageObj.onload = () => {
      try {
        const konvaImage = new Konva.Image({
          image: imageObj,
          id: `generated-image-${Date.now()}`,
        });
        scaleImageToFitCanvas(konvaImage, canvas);

        if (activeProject?.project_id && agentId && imagePrompt) {
          const fileName = `Generated WebP Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            imageUrl,
            fileName,
            planId,
            imagePrompt,
          ).then(() => {
            window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
          }).catch((error) => {
            console.error('Error storing WebP image:', error);
          });
        }

        resolve();
      } catch (error) {
        console.error('Error adding WebP image to canvas:', error);
        reject(error);
      }
    };

    imageObj.onerror = (error) => {
      console.error('Failed to load WebP image:', error);
      reject(new Error('Failed to load WebP image'));
    };

    imageObj.src = imageUrl;
  });
};

export const GenerateImagePanel = ({
  canvas,
  agentId,
  planId,
}: GenerateImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof ImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();
  const {
    setGenerating, loadingStates,
  } = useCanvasLoading();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof ImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setGenerating(true);

    try {
      if (selectedStyle && selectedStyle.option === 'ghibli') {
        const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
        const endpoint = `${baseUrl}/${agentId}/recraft-image`;

        const requestBody = {
          prompt: `Studio Ghibli, ${imagePrompt}, Hand-drawn animated illustration, Watercolor techniques, Soft organic lines, Warm color palette`,
          width: 1024,
          height: 1024,
          count: 1,
          model: 'recraftv3',
          style: 'digital_illustration',
          substyle: '',
          styleId: '',
          seed: seed,
          controls: {
            artistic_level: 5,
            no_text: false,
          },
        };

        const response = await fetch(endpoint, {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          if (response.status === 422 && errorData.error?.includes('NSFW')) {
            throw new Error('Content flagged as inappropriate. Please try a different prompt.');
          }
          throw new Error(errorData.error || 'Failed to generate vector image');
        }

        const result = await response.json();

        if (result.success && result.images && result.images.length > 0) {
          const imageUrl = result.images[0];

          if (canvas) {
            await addWebPImageToCanvas(imageUrl, canvas, activeProject, agentId, planId, imagePrompt);
          } else {
            throw new Error('Canvas not available');
          }

          trackContentEvent('image', {
            prompt: imagePrompt,
            imageStyle: selectedStyle?.option || 'none',
          });

          toast.success('Vector image generated and added to canvas!');
        } else {
          throw new Error('No image data received from Recraft');
        }

      } else {
        const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
        const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

        const requestBody: any = {
          description: imagePrompt,
          planId: planId || 'new-post',
          seed: seed,
          guidanceScale: guidanceScale,
        };

        if (selectedStyle && selectedStyle.option !== 'none') {
          requestBody.style = [selectedStyle.option];
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          toast.error('Failed to generate image');
          setError(`${errorData.error.includes("NSFW") ? `${errorData.error} ` : ""}` || 'Failed to generate image');
          setGenerating(false);
          return
        }

        const imageData = await response.json();

        if (imageData && imageData.filepath && canvas) {
          if (!canvas.children || canvas.children.length === 0) {
            console.error('Canvas not ready for image addition');
            toast.error('Canvas not ready. Please try again.');
            setGenerating(false);
            return;
          }

          const imageUrl = getPath(imageData.filepath);

          const imageObj = new window.Image();
          imageObj.crossOrigin = 'anonymous';
          imageObj.onload = () => {
            const konvaImage = new Konva.Image({
              image: imageObj,
              id: `generated-image-${Date.now()}`,
            });
            scaleImageToFitCanvas(konvaImage, canvas);
          };
          imageObj.onerror = (error) => {
            console.error('Failed to load image:', error);
            toast.error('Failed to load generated image');
          };
          imageObj.src = imageUrl;

          if (activeProject?.project_id && agentId) {
            const fileName = `Generated Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
            await projectImageStorage.addGeneratedImage(
              activeProject.project_id,
              agentId,
              imageUrl,
              fileName,
              planId,
              imagePrompt,
            );
            window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
          }

          trackContentEvent('image', {
            prompt: imagePrompt,
            imageStyle: selectedStyle?.option || 'none',
          });

          toast.success('Image generated and added to canvas!');
        } else {
          throw new Error('Invalid response from image generation API');
        }
      }

    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Image Style</h3>
            <p className="text-gray-400 text-sm">Create and add images using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className="pr-2">
                {Object.entries(ImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-2xl transition-all duration-300 text-left group aspect-square bg-gradient-to-br from-neutral-800/80 to-neutral-900/80 backdrop-blur-sm hover:from-neutral-700/90 hover:to-neutral-800/90 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/20 active:scale-[0.98]"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              {style.option === "none" ? (
                                <div className="flex items-center justify-center h-full rounded-xl bg-gradient-to-br from-neutral-800 via-neutral-700 to-neutral-800 text-white text-xs font-medium border border-neutral-600/30 group-hover:from-neutral-700 group-hover:via-neutral-600 group-hover:to-neutral-700 transition-all duration-300">
                                  <div className="text-center">
                                    <div className="text-lg mb-1">✨</div>
                                    <div>Create Your Own Style</div>
                                  </div>
                                </div>
                              ) : (
                                <div className="relative h-full w-full rounded-xl overflow-hidden">
                                  <Image
                                    src={`/images/style-samples/${style.option}.png`}
                                    alt={style.label}
                                    width={400}
                                    height={400}
                                    quality={40}
                                    className='rounded-xl h-full w-full object-cover transition-transform duration-300 group-hover:scale-105'
                                  />
                                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
                                </div>
                              )}
                            </div>
                            <div className="text-[10px] text-center absolute bottom-3 left-3 right-3 text-white bg-black/60 rounded-lg py-1.5 px-2.5 backdrop-blur-sm truncate shadow-lg border border-white/10 group-hover:bg-black/70 transition-all duration-300">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Generate Image</h3>
                <p className="text-gray-400 text-sm">
                  Style: <span className="text-violets-are-blue">{selectedStyle?.label || 'None'}</span>
                </p>
              </div>
            </div>
          </div>
          <div className='flex flex-col space-y-4'>
            <label htmlFor="image-prompt" className="text-white font-medium text-sm">
              Describe your image
              <TextArea
                ref={imagePromptRef}
                name="image-prompt"
                id="image-prompt"
                width='w-full'
                disabled={loadingStates.isGenerating}
                placeholder='Describe the image you want to generate...'
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !loadingStates.isGenerating && imagePrompt.trim()) {
                    e.preventDefault();
                    handleGenerate();
                  }
                }}
              />
            </label>
            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between items-center">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />

              </div>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Guidance Scale: {guidanceScale}
              </label>
              <input
                type="range"
                min="0"
                max="20"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              onClick={handleGenerate}
              disabled={loadingStates.isGenerating || !imagePrompt.trim()}
              variant='gradient'
              size='md'
              width='w-full'
            >
              {loadingStates.isGenerating ? 'Generating...' : 'Generate Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
